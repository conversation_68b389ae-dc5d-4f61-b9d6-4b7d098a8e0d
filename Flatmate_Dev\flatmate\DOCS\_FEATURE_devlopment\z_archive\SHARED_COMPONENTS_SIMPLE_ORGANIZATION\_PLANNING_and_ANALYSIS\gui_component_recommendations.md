# GUI Component Design Recommendations

**Date**: July 22, 2025  
**Status**: Recommendation  

## 1. Executive Summary

This document outlines recommendations for organizing and creating shared GUI components. The investigation confirms that a previous refactoring failed due to **unnecessary complexity and widget wrapping**, which broke the QSS styling mechanism. 

The core recommendation is to **favour simple, direct patterns** that are already working within the codebase. We should create reusable components through **composition** and **simple inheritance**, avoiding abstract base classes and wrappers that add no value.

## 2. Investigation Findings

### Finding 1: A "Labelled Menu Widget" Already Exists and Works

Your suspicion was correct. The shared components directory already contains `OptionMenuWithLabel` and `OptionMenuWithLabelAndButton`. These are perfect examples of the recommended pattern.

**File**: `fm/gui/_shared_components/widgets/option_menus.py`

```python
class OptionMenuWithLabel(QWidget):
    """
    A combo box with a label in a vertical layout.
    """
    def __init__(self, label_text, options, parent=None):
        super().__init__(parent)
        # ... UI is built by adding QLabel and QComboBox to a layout
        self.label = QLabel(label_text)
        self.label.setObjectName("subheading") # Works with QSS
        self.combo_box = QComboBox()
        # ...
```

**Why this works:**
*   **Composition**: It inherits from `QWidget` and *composes* other standard widgets (`QLabel`, `QComboBox`).
*   **Styling**: It uses `setObjectName` directly on the child widgets, making them targetable by the existing QSS stylesheet.
*   **Simplicity**: It is simple, self-contained, and easy to understand.

### Finding 2: The `update_data` Module Does Not Use These Components

A search confirms that the `update_data` module is not currently using `OptionMenuWithLabel`, representing a clear opportunity for code simplification and consistency by refactoring it to use the shared component.

### Finding 3: The Refactoring Failure Was Caused by Widget Wrapping

The `gui_refactoring_failure_analysis_250722.md` clearly shows that the failed refactor wrapped functional widgets (like `QPushButton`) inside a `BaseWidget` container. This broke the QSS selectors.

**Broken Pattern (for illustration):**
```python
# The BROKEN pattern from the failed refactor
class ActionButton(BaseWidget): # Inherits QWidget, not QPushButton
    def _setup_ui(self):
        # The actual button is a child, not the component itself
        self._button = QPushButton(self) 
        layout = QVBoxLayout(self)
        layout.addWidget(self._button)
```
This approach prevents `QPushButton[type="action_btn"]` from working because the `ActionButton` itself is not a `QPushButton`, and the property is not set on the inner button.

## 3. Recommendations & Design Patterns

To ensure a robust, maintainable, and simple component library, the following principles should be adopted.

### Pattern 1: Favour Composition for Compound Widgets

For any component that combines more than one widget, use the **composition** pattern, modeled after `OptionMenuWithLabel`.

*   **Inherit from `QWidget`**.
*   Create standard Qt widgets (`QLabel`, `QComboBox`, `QPushButton`, etc.) as children.
*   Arrange them using a layout.
*   Expose necessary signals and methods (`button_clicked`, `get_selected_option`).
*   Use `setObjectName` or `setProperty` on the child widgets to hook into the QSS stylesheet.

### Pattern 2: Use Simple Inheritance for Simple Variants

For components that are just simple variations of a single base widget (like the existing buttons), continue using **simple, direct inheritance**.

```python
# The CORRECT, simple pattern for buttons
class ActionButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setProperty("type", "action_btn")
```
This is clean, efficient, and works perfectly with the existing QSS styling.

### Pattern 3: Centralize Styling in QSS

Continue to manage all styling in the central `.qss` file. Design components specifically to be styled via **type, property, and ID selectors**, as is current practice. Avoid any design (like wrappers) that interferes with this.

### Pattern 4: Absolutely No Unnecessary Abstractions

As learned from the failed refactor, we must strictly avoid:
*   **Abstract Base Classes** (`BaseWidget`).
*   **Configuration Objects** (`ButtonConfig`).
*   **Wrapper Widgets** that hide the underlying styled component.
*   **Factory Patterns**.

These add complexity with no benefit for our use case.

## 4. Proposed Plan

1.  **Create New Components**: Follow the patterns above to create any new shared components (e.g., `labels.py` as proposed in the original planning document).
2.  **Refactor Existing Modules**: Incrementally refactor modules like `update_data` to use the standardized, shared components.
3.  **Validate**: After each change, run the application to ensure that styling and functionality remain intact.
