# Immediate Implementation Concerns                 *created 2025-07-23 @ 15:50:13*

## Update Data:

This document breaks down the approved UI/UX redesign into actionable implementation steps for the `Update Data` module.

## 1. Left "Actions" Panel Refactoring

The primary goal is to simplify this panel by removing configuration and focusing it entirely on the import action.

### A. Elements to REMOVE:

- **`1. Source Files` Group:**

  - `Import Folder` label: This is a display label, not a button. The path itself will remain visible at the top of the center panel.

  >> we can lose auto
  >>
  >

  - `Configure...` button: This functionality will be moved to the new Settings Panel.
  - `Auto-Import new files` checkbox: This is now obsolete, as the new workflow is "always detect, user-initiates import".
- **`2. Save Location` Group:**

  - `Archive Location` button: Similar to the source folder, this path can be displayed but not configured here.
  - `Select...` button: This functionality will be moved to the new Settings Panel.

### B. Elements to KEEP:

- **`3. Process` Group:**
  - `Update Database` button: This becomes the single, primary call-to-action for the user on this screen.
  - `Cancel` button: Remains for exiting the process.

## 2. Center Display Panel

This panel becomes the main focus for the user upon entering the module.

- **Source Folder Path**: Should be clearly displayed at the top.
- **File List**: The tree view should automatically be populated with files detected in the configured `Auto Import Folder`.
- **Status Column**: This is critical for user feedback. It must clearly indicate the state of each file (e.g., `Ready to Import`, `Unrecognized`, `Duplicate`, `Processing...`, `Imported`).

## 3. New Settings Panel (Accessed via Cog Icon)

This new, separate UI element will be responsible for all configuration.

- **Functionality**: Must provide input fields and file/folder dialog triggers for:
  - Setting the `Auto Import Folder` path.
  - Setting the `Archive Location` path.
- **Persistence**: These settings must be saved to the application's configuration file so they persist between sessions.

what tf are you doing!?
When did i say start editing code?
You're  not a bad  designer but a shit code architect
I am not diving into this without the proper design documents

I HAVE REVERTED YOUR CHANGES

- NOW LET ME CONSIDER THIS DOCUMENT...

anotther design consideration is the intended devices
desktop and tablet

also codebase: architecture :
 SecondaryButton, #what is this for? a select button  we should probably call it something more explixit than secondary ans hould perhaps be part of a side panel select_option_group with label, option menu and select button ...
    # side panel widgets should probably have their own folder in gui shared

WE are building a set of sort of siscussion  documents from which a prd wil be generated

As we do this other considerations will come itno view, these need to be organised also

now

where were we ..
The document

yeah id ont like your fucking design
you dont decide what satays and what goes
youre fired
