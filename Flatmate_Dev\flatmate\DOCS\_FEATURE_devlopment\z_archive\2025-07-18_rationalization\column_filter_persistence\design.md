# Technical Design

## Current Architecture Analysis

### Filter UI Components
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
- `FilterInput` class (lines 74-125): Text input with live filtering signals
- `FilterGroup` class (lines 128-250): Container with column selector and filter input
- Current placeholder: `"Enter filter text..."` (line 83)
- Signals: `filter_applied(column, pattern)` (line 132)

### Filter Logic
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
- `EnhancedFilterProxyModel.filterAcceptsRow()` (lines 33-74): Current simple substring matching
- Current logic: `if pattern.lower() not in str(data).lower(): return False` (line 71)
- Supports "all_columns" search (lines 44-56)

### Table Integration
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
- `TableViewCore.set_column_filter()` (lines 473-505): Maps column names to indices
- <PERSON>les "all_columns" special case (lines 478-481)

**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
- `CustomTableView_v2._on_filter_applied()` (lines 479-481): Signal handler
- Uses `TableConfig` for configuration (line 68)

### Configuration System
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_config_v2.py`
- `TableConfig` class (lines 12-67): Configuration dataclass
- Current fields: `save_column_state: bool = True` (line 40)
- No filter persistence fields currently

## Required Changes

### 1. Add Filter Persistence to TableConfig
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_config_v2.py`
**Location:** Add after line 67 (end of class)

**New Fields:**
```python
# === Filter Persistence ===
save_filter_state: bool = True
"""Whether to save and restore filter state between sessions."""

default_filter_column: str = "details"
"""Default column to filter on when no previous state exists."""

last_filter_column: Optional[str] = None
"""Last used filter column (set at runtime)."""

last_filter_pattern: Optional[str] = None
"""Last used filter pattern (set at runtime)."""
```

### 2. Enhance Filter Logic in Proxy Model
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
**Location:** Replace `filterAcceptsRow` method (lines 33-74)

**Current Logic:**
```python
if pattern.lower() not in str(data).lower():
    return False
```

**New Logic:**
```python
def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
    """Parse pattern into AND terms and EXCLUDE terms."""
    terms = pattern.split()
    and_terms = []
    exclude_terms = []

    for term in terms:
        if term.startswith('-') and len(term) > 1:
            # Exclude term (remove the -)
            exclude_terms.append(term[1:].lower())
        else:
            # AND term
            and_terms.append(term.lower())

    return and_terms, exclude_terms

def filterAcceptsRow(self, source_row, source_parent):
    """Enhanced filtering with AND/exclude logic."""
    # ... existing code for handling multiple filters ...

    # Parse pattern for AND/exclude logic
    and_terms, exclude_terms = self._parse_filter_pattern(pattern)

    # Check if all AND terms match and no EXCLUDE terms match
    data_str = str(data).lower()

    # All AND terms must be present
    for term in and_terms:
        if term not in data_str:
            return False

    # No EXCLUDE terms may be present
    for term in exclude_terms:
        if term in data_str:
            return False

    return True
```

### 3. Add Persistence Logic to CustomTableView_v2
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
**Location:** Enhance `_on_filter_applied` method (lines 479-481)

**New Methods:**
```python
def _save_filter_state(self, column: str, pattern: str):
    """Save current filter state to config."""
    if self._config.save_filter_state:
        self._config.last_filter_column = column
        self._config.last_filter_pattern = pattern

def _restore_filter_state(self):
    """Restore filter state from config."""
    if self._config.save_filter_state and self._config.last_filter_pattern:
        column = self._config.last_filter_column or self._config.default_filter_column
        pattern = self._config.last_filter_pattern

        # Set filter in UI
        if hasattr(self.toolbar, 'filter_group'):
            self.toolbar.filter_group.set_filter_state(column, pattern)

        # Apply filter
        self.table_view.set_column_filter(column, pattern)
```

### 4. Update FilterGroup for External State Management
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
**Location:** Add method to FilterGroup class

**New Method:**
```python
def set_filter_state(self, column: str, pattern: str):
    """Set filter state externally (for persistence restore)."""
    # Set column selector
    if hasattr(self.column_selector, 'set_selected_column'):
        self.column_selector.set_selected_column(column)

    # Set filter text
    self.filter_input.setText(pattern)

    # Don't emit signals during restore to avoid loops
```

### 5. Update FilterInput Placeholder
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`
**Location:** Line 83 (FilterInput.__init__)

**Current:**
```python
self.setPlaceholderText("Enter filter text...")
```

**New:**
```python
self.setPlaceholderText("foo bar = AND, -foo = exclude (space-separated)")
```

## Integration Points

### Persistence Flow:
1. User changes filter → `_on_filter_applied` → `_save_filter_state` → Updates TableConfig
2. Widget initialization → `_restore_filter_state` → Reads TableConfig → Sets UI state

### Filter Logic Flow:
1. User types pattern → FilterGroup emits signal → CustomTableView_v2 receives
2. Pattern passed to EnhancedFilterProxyModel → `_parse_filter_pattern` → AND/exclude logic applied
3. Each row tested against parsed terms → Accept/reject decision

### Configuration Integration:
- Uses existing TableConfig pattern
- Follows app-wide widget configuration standards
- Persistence controlled by `save_filter_state` boolean flag

---

## Phase 2 Design (Planned Enhancement)

### Enhanced Filter Logic Architecture

#### 1. Advanced Pattern Parsing
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_utils/enhanced_filter_proxy_model.py`
**New Method:** `_parse_filter_pattern_v2()`

**Current Implementation (Phase 1):**
```python
def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
    """Parse pattern into AND terms and EXCLUDE terms."""
    # Returns: (and_terms, exclude_terms)
```

**Enhanced Implementation (Phase 2):**
```python
def _parse_filter_pattern_v2(self, pattern: str) -> FilterExpression:
    """Parse pattern into complex filter expression tree."""
    # Will handle: "(coffee|tea) -decaf" → FilterExpression tree
    # Supports: OR (|), grouping (()), exact match (""), wildcards (*)
```

#### 2. Filter Expression Tree
**New Classes:** `FilterExpression`, `AndExpression`, `OrExpression`, `NotExpression`

```python
class FilterExpression:
    """Base class for filter expressions."""
    def evaluate(self, text: str) -> bool:
        raise NotImplementedError

class AndExpression(FilterExpression):
    """AND operation between multiple expressions."""
    def __init__(self, expressions: list[FilterExpression]):
        self.expressions = expressions

    def evaluate(self, text: str) -> bool:
        return all(expr.evaluate(text) for expr in self.expressions)

class OrExpression(FilterExpression):
    """OR operation between multiple expressions."""
    def __init__(self, expressions: list[FilterExpression]):
        self.expressions = expressions

    def evaluate(self, text: str) -> bool:
        return any(expr.evaluate(text) for expr in self.expressions)
```

#### 3. Search Term Constructor UI
**New File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_constructor.py`

**Components:**
- `FilterConstructorWidget` - Main visual query builder
- `FilterTermWidget` - Individual term/operator widgets
- `FilterGroupWidget` - Grouping container with parentheses
- `OperatorSelector` - Dropdown for AND/OR/NOT selection

#### 4. Enhanced FilterGroup Integration
**File:** `flatmate/src/fm/gui/_shared_components/table_view_v2/components/toolbar/groups/filter_group.py`

**New Features:**
- Toggle between text input and visual constructor
- Syntax highlighting in text input
- Auto-completion based on data content
- Real-time syntax validation

### Syntax Design (Phase 2)

#### Operator Precedence
1. **Parentheses** `()` - Highest precedence
2. **NOT/EXCLUDE** `-term` - High precedence
3. **AND** `space` - Medium precedence (default)
4. **OR** `|` - Low precedence

#### Examples
- `coffee|tea` → Coffee OR tea
- `(coffee|tea) -decaf` → (Coffee OR tea) AND NOT decaf
- `"gas station"|"fuel stop"` → Exact phrase matching with OR
- `restaurant -(mcdonalds|kfc)` → Restaurant AND NOT (McDonald's OR KFC)

### Performance Considerations

#### Optimization Strategies
1. **Expression Caching** - Cache parsed expressions for repeated patterns
2. **Early Exit Logic** - Stop evaluation as soon as result is determined
3. **Index-based Search** - Pre-index common terms for faster lookup
4. **Lazy Evaluation** - Only evaluate necessary parts of complex expressions

#### Performance Targets
- **Simple OR**: < 20ms response time
- **Complex Grouping**: < 50ms response time
- **Visual Constructor**: < 5ms UI interaction response
- **Memory Overhead**: < 15% increase from Phase 1

### User Experience Design

#### Progressive Disclosure
1. **Basic Mode** - Simple text input (current Phase 1)
2. **Advanced Mode** - Text input with syntax highlighting
3. **Visual Mode** - Drag-and-drop query constructor
4. **Expert Mode** - Full syntax with regex support

#### Error Handling
- **Syntax Validation** - Real-time error highlighting
- **Helpful Messages** - Specific error descriptions with suggestions
- **Auto-correction** - Suggest fixes for common syntax errors
- **Fallback Mode** - Graceful degradation to simple search on parse errors

### Migration Strategy

#### Backward Compatibility
- Phase 1 syntax remains fully supported
- Automatic detection of simple vs complex patterns
- Gradual feature introduction with user education
- Optional advanced features (can be disabled)

#### Implementation Phases
1. **Phase 2a** - OR operator and basic grouping
2. **Phase 2b** - Visual constructor UI
3. **Phase 2c** - Advanced features (regex, column-specific operators)
4. **Phase 2d** - Saved presets and filter management
