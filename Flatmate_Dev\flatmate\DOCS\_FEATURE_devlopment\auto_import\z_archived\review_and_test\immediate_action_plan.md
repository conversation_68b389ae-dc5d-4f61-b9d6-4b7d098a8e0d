# Immediate Action Plan: Auto-Import Feature Fixes

**Date**: 2025-07-21  
**Priority**: CRITICAL - Feature Currently Non-Functional  
**Target**: Make feature minimally viable for user  

## Critical Issues to Fix (In Order)

### 1. Fix Dialog Browse Button Functionality ⚠️ CRITICAL
**Problem**: QFileDialog browse buttons don't work in AutoImportConfigDialog
**Impact**: Users cannot configure auto-import paths
**Files**: `flatmate/src/fm/gui/dialogs/auto_import_config_dialog.py`

**Immediate Actions**:
- [ ] Test QFileDialog.getExistingDirectory() calls in dialog
- [ ] Debug parent widget parameter issues
- [ ] Verify dialog modal settings
- [ ] Test with actual user interaction

**Quick Test**:
```python
# Test this in isolation
from PySide6.QtWidgets import QApplication, QFileDialog
app = QApplication([])
folder = QFileDialog.getExistingDirectory(None, "Test")
print(f"Selected: {folder}")
```

### 2. Fix Source Selection State Management ⚠️ CRITICAL
**Problem**: Combo box stuck on "Set auto import folder..." after configuration
**Impact**: Normal file selection workflow broken
**Files**: `flatmate/src/fm/modules/update_data/ud_presenter.py`

**Current Issue**: State reset logic may not be working properly
**Immediate Fix**: Debug the `self.view.set_source_option(last_source_option)` call

### 3. Add Basic Auto-Import Status Display 🔧 HIGH
**Problem**: No feedback about auto-import configuration or status
**Impact**: Users don't know if feature is working
**Location**: Center panel or info bar

**Quick Solution**: Add simple status text showing:
- "Auto-import: Disabled" or "Auto-import: Monitoring [folder]"
- Whether folders exist and are accessible

### 4. Create Default Folders on Configuration 🔧 HIGH
**Problem**: No folders created in Downloads directory
**Impact**: Users see no evidence feature is configured
**Files**: `AutoImportConfigDialog` and `AutoImportManager`

**Immediate Fix**: Ensure folder creation happens when dialog saves settings

## Quick Diagnostic Steps

### Test Dialog Functionality
1. Open auto-import dialog manually
2. Test each browse button individually
3. Check if paths are actually saved to config
4. Verify folder creation logic

### Test State Management
1. Select auto-import option
2. Cancel dialog - check if combo resets
3. Save dialog - check if combo resets
4. Verify normal file selection still works

### Test Auto-Import Service
1. Check if AutoImportManager starts correctly
2. Verify folder monitoring is active
3. Test file detection with dummy CSV file

## User Experience Quick Wins

### Immediate UX Improvements
1. **Clear Status Display**: Show auto-import status prominently
2. **Folder Validation**: Show green/red indicators for folder accessibility
3. **Success Feedback**: Clear confirmation when configuration saves
4. **Reset Behavior**: Ensure UI returns to normal state after configuration

### Future UX Redesign (Phase 2)
- Move auto-import to right panel as context-specific setting
- Add dedicated auto-import status area in center panel
- Create proper settings/preferences section

## Testing Protocol

### Before Declaring Fixed
- [ ] Dialog browse buttons work and save paths
- [ ] Folders are created in Downloads directory
- [ ] Source combo box resets to previous selection
- [ ] Normal file selection workflow unaffected
- [ ] Auto-import status visible to user
- [ ] Configuration persists across app restarts

### User Validation
- [ ] User can complete configuration without confusion
- [ ] User can see evidence that feature is configured
- [ ] User can return to normal file processing workflow
- [ ] User understands current auto-import status

## Success Criteria for Phase 1 Fix

**Minimum Viable Feature**:
1. ✅ Dialog browse buttons work
2. ✅ Folders created and visible
3. ✅ Configuration saves and persists
4. ✅ UI state management works
5. ✅ Basic status feedback provided
6. ✅ Normal workflow unaffected

**User Acceptance**:
- User can successfully configure auto-import
- User can see that feature is configured
- User can continue with normal file processing
- No confusion about feature status

---

**Next Steps**: Address items 1-4 in order, then test with user before proceeding to Phase 2 UX redesign.
