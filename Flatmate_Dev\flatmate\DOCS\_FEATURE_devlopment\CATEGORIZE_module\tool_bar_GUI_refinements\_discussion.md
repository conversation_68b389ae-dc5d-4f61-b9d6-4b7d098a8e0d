# Toolbar GUI Refinements — Discussion

## 1. Text Entry Field
- Should expand horizontally to take up available space in the toolbar.

## 2. Clear <PERSON><PERSON> ("x")
- Preferred icon: Material "cancel" (x) icon
  - `flatmate/resources/icons/cancel_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`
- Alternative: Material backspace icon
  - `flatmate/resources/icons/backspace_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`
- Text label should just be `x` (no extra text)
- **Current preference:** `cancel` (x) icon
- the search tool should have a search icon on the left
  `flatmate\resources\icons\search_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`

## 3. Apply <PERSON><PERSON> (Tick)
- Should be the same green as current apply button
- Icon: White tick (Material check)
  - `flatmate/resources/icons/check_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`

## 4. Columns Dropdown
- Should use the visibility or view "eye" icon already used in the nav bar
but that is sorted by file for the nav bar 
in resources the file is currently :
`flatmate\resources\icons\eye_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24.svg`
----

## Icon Management Discussion
- Currently implemented icons are in `gui/icons`
- new icons are in flatmate/ `resources/icons`
- Consider further folder organisation by source (e.g. `material/`, `custom/`) # ?
- All icons should be SVG for scalability and styling

---

**Summary:**
- Toolbar should be visually clean and use consistent Material icons (SVG)
- text box needs to be prioritised for space 
- Prioritise space efficiency and clarity
- Use existing icons where possible
- icon management needs to be considered 

# possible sources of relevant DOCS
flatmate\DOCS\_ARCHITECTURE\_UI_system\
- may be out of date 

discussion points: architectural concerns app wide icon management
>> the resources folder is easy to access and drop icons to
- we want to be able to manage and replace icons easily