# Test Results: Table Search & Filtering Refactor (Package-Based Search)

## User Test Observations (2025-07-19)

### Filtering Logic
- Entering `nbc -(`: everything disappears when typing the `-`.
- Typing `(` after `-` causes lag before results reappear.
- Entering anything inside the brackets (`nbc -(x`) causes severe lag or UI freeze.
- `nbc (x2` becomes impossible to enter.
- Live filtering may not be compatible with more advanced filtering logic—needs reconsideration.
- What works well and quickly: `nbc -xt -wk` (no brackets). This is intuitive; brackets may not be needed.

### Visible Columns
- The visible columns list includes every possible column, including:
  - `DB UID` (internal, should not be visible)
  - `Source UID` (db name, not useful to users)
  - `Unique ID` (the only one that should be visible)
  - `Is Deleted` (system column, should not be visible)
- Only `Unique ID` should be visible; others are internal/system and should be hidden from users.

### Stability and Performance
- At one point, everything disappeared from the table and would not come back.
- System-wide lag observed; possible recursive logic or value error not being caught.

### Log File Reference
- See: `.flatmate\logs\flatmate-20250719_010725.log`

---
(Extracted from: `../___CURRENT_ISSUES/user-test-notes_categorise_Table_view.md` lines 24–53)

## User Test Notes
- When a row is selected (or a cell in that row) and you click out of the table view, the highlighting should not turn the text dark grey. The column can remain highlighted, but the text should stay white.
- Text in non-editable columns (like "Details") should be selectable and copyable.
- It should be possible to select multiple cells in a column for applying a tag.
- (Future) Option to apply tags to all matching rows—this enters categorisation territory and needs further design.

## Log File Reference
- See: `C:/Users/<USER>/.flatmate/logs/flatmate-20250719_010725.log`

## Issues & Observations
- [ ] Highlighting behaviour for selected rows/cells when focus is lost needs review.
- [ ] Ensure text in non-editable columns is selectable and copyable.
- [ ] Multi-cell selection for tagging is not yet implemented.
- [ ] Bulk tagging for all matches is a potential future enhancement.

## Next Steps
- Address UI/UX issues with row/cell highlighting and selection.
- Confirm and test copy/select behaviour in all relevant columns.
- Design and implement multi-cell selection and tagging.
- Discuss requirements and workflow for bulk tagging/categorisation.

---
For detailed user notes, see: `../___CURRENT_ISSUES/user-test-notes_categorise_Table_view.md`
