# Auto Import Feature - Implementation Tasks

**Status**: Ready for Implementation
**Estimated Duration**: Single 2-3 hour development session
**Architecture**: "One Canvas, Two Workflows" - Morphic UI approach
**Updated**: 2025-07-21 (Consolidated from multiple planning documents)

---

## Overview

The auto-import feature implements a **morphic UI approach** where a single interface adapts between two workflows based on the "Update Database" checkbox state. This eliminates the "clunky popup" and creates an integrated, intuitive user experience.

### Core Concept: One Canvas, Two Workflows

- **Database Mode** (default): Streamlined auto-import with dashboard view
- **File Utility Mode**: Legacy CSV processing functionality
- **Morphic UI**: Dynamic visibility changes without rebuilding components
- **Single Control**: `[✓] Update Database` checkbox drives all UI adaptation

---

## Implementation Phases

### Phase 1: Morphic UI Implementation [60 minutes]

#### Task 1.1: Update Database Checkbox Handler [20 min]
**File**: `flatmate/src/fm/modules/update_data/ud_presenter.py`

Enhance the existing `_handle_update_database_change` method to drive UI morphing:

```python
def _handle_update_database_change(self, checked: bool):
    """Handle update database checkbox changes - drives UI morphing."""
    # Store the state
    ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

    # Configure UI based on mode
    if checked:
        # Database Mode: Show auto-import controls, hide save location complexity
        self.view.left_buttons.show_auto_import_controls()
        self.view.left_buttons.simplify_save_location()  # Archive folder only
        self.view.center_display.show_dashboard_pane()
    else:
        # File Utility Mode: Hide auto-import, show full file controls
        self.view.left_buttons.hide_auto_import_controls()
        self.view.left_buttons.show_full_file_controls()
        self.view.center_display.show_file_staging_pane()

    # Update button text
    button_text = "Update Database" if checked else "Process Files"
    self.view.left_buttons.set_process_button_text(button_text)
```

**Verification**: Checkbox toggles UI elements correctly between modes

#### Task 1.2: Left Panel Widget Enhancements [20 min]
**File**: `flatmate/src/fm/modules/update_data/views/left_panel/widgets/widgets.py`

Add morphic behavior methods to `LeftPanelButtonsWidget`:

```python
def show_auto_import_controls(self):
    """Show auto-import related controls for database mode."""
    # Make auto-import option visible in source dropdown
    self.source_widget.show_auto_import_option()
    # Show auto-import configuration button if needed
    if hasattr(self, 'auto_import_config_btn'):
        self.auto_import_config_btn.show()

def hide_auto_import_controls(self):
    """Hide auto-import controls for file utility mode."""
    self.source_widget.hide_auto_import_option()
    if hasattr(self, 'auto_import_config_btn'):
        self.auto_import_config_btn.hide()

def simplify_save_location(self):
    """Simplify save location to archive folder only."""
    self.save_widget.set_mode("archive_only")
    self.save_widget.set_label("Archive Folder:")

def show_full_file_controls(self):
    """Show full file processing controls."""
    self.save_widget.set_mode("full_control")
    self.save_widget.set_label("Save Location:")

def set_process_button_text(self, text: str):
    """Update the main process button text."""
    self.process_button.setText(text)
```

**Verification**: UI elements show/hide correctly based on mode

#### Task 1.3: Center Panel Dashboard Implementation [20 min]
**File**: `flatmate/src/fm/modules/update_data/views/center_panel/_panel_manager.py`

Add dashboard and file staging pane switching:

```python
def show_dashboard_pane(self):
    """Show dashboard pane for database mode."""
    # Hide file tree, show dashboard
    if hasattr(self, 'file_tree_pane'):
        self.file_tree_pane.hide()

    # Show or create dashboard pane
    if not hasattr(self, 'dashboard_pane'):
        self.dashboard_pane = self._create_dashboard_pane()

    self.dashboard_pane.show()
    self.current_pane = 'dashboard'

def show_file_staging_pane(self):
    """Show file staging pane for file utility mode."""
    # Hide dashboard, show file tree
    if hasattr(self, 'dashboard_pane'):
        self.dashboard_pane.hide()

    # Show file tree for staging
    if not hasattr(self, 'file_tree_pane'):
        self.file_tree_pane = self._create_file_tree_pane()

    self.file_tree_pane.show()
    self.current_pane = 'file_staging'

def _create_dashboard_pane(self):
    """Create dashboard pane with status and activity info."""
    # Implementation for dashboard creation
    pass
```

**Verification**: Center panel switches between dashboard and file staging correctly

---

### Task 5: Presenter Integration [20 min]
**File**: [`flatmate/src/fm/modules/update_data/presenter.py`](flatmate/src/fm/modules/update_data/presenter.py:1)

Add auto-import toggle functionality to the presenter:

```python
def toggle_auto_import(self, enabled: bool) -> bool:
    """Enable or disable auto-import monitoring."""
    from fm.core.services.auto_import_manager import AutoImportManager
    
    manager = AutoImportManager()
    
    if enabled:
        try:
            manager.start()
            return True
        except Exception as e:
            log.error(f"Failed to start auto-import: {e}")
            return False
    else:
        manager.stop()
        return True

def get_auto_import_config(self) -> dict:
    """Get current auto-import configuration."""
    from fm.core.services.auto_import_manager import AutoImportManager
    
    manager = AutoImportManager()
    return {
        'enabled': manager.enabled,
        'import_path': str(manager.import_path),
        'archive_path': str(manager.archive_path),
        'failed_path': str(manager.failed_path)
    }
```

**Verification**: Presenter methods integrate with existing MVP pattern

---

### Task 6: UI Toggle Component [20 min]
**File**: [`flatmate/src/fm/modules/update_data/views/settings_panel.py`](flatmate/src/fm/modules/update_data/views/settings_panel.py:1)

Create toggle switch component using existing patterns:

```python
from PySide6.QtWidgets import QCheckBox, QVBoxLayout, QLabel

class AutoImportSettingsWidget(QWidget):
    """Settings widget for auto-import configuration."""
    
    def __init__(self, presenter, parent=None):
        super().__init__(parent)
        self.presenter = presenter
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Toggle switch
        self.toggle_switch = QCheckBox("Enable Auto Import")
        self.toggle_switch.toggled.connect(self.on_toggle_changed)
        layout.addWidget(self.toggle_switch)
        
        # Status label
        self.status_label = QLabel("Auto-import is disabled")
        layout.addWidget(self.status_label)
        
        # Load initial state
        self.load_current_state()
    
    def load_current_state(self):
        """Load current auto-import state."""
        config = self.presenter.get_auto_import_config()
        self.toggle_switch.setChecked(config['enabled'])
        self.update_status(config)
    
    def on_toggle_changed(self, checked):
        """Handle toggle change."""
        success = self.presenter.toggle_auto_import(checked)
        if success:
            config = self.presenter.get_auto_import_config()
            self.update_status(config)
        else:
            self.toggle_switch.setChecked(False)
    
    def update_status(self, config):
        """Update status display."""
        if config['enabled']:
            self.status_label.setText(
                f"Monitoring: {config['import_path']}"
            )
        else:
            self.status_label.setText("Auto-import is disabled")
```

**Verification**: Widget integrates with existing settings panel pattern

---

### Task 7: Integration Testing [20 min]
**File**: [`flatmate/src/fm/modules/update_data/tests/test_auto_import.py`](flatmate/src/fm/modules/update_data/tests/test_auto_import.py:1)

Create integration test using existing test patterns:

```python
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch

from fm.core.services.auto_import_manager import AutoImportManager

class TestAutoImportIntegration:
    """Integration tests for auto-import functionality."""
    
    @pytest.fixture
    def temp_import_dir(self):
        """Create temporary import directory."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    def test_auto_import_manager_initialization(self, temp_import_dir):
        """Test manager initializes with correct paths."""
        with patch('fm.core.config.config.get_value') as mock_config:
            mock_config.side_effect = lambda key, default: {
                'auto_import.enabled': True,
                'auto_import.import_path': str(temp_import_dir),
                'auto_import.archive_path': str(temp_import_dir / 'archive'),
                'auto_import.failed_path': str(temp_import_dir / 'failed')
            }.get(key, default)
            
            manager = AutoImportManager()
            assert manager.enabled is True
            assert manager.import_path == temp_import_dir
            assert manager.archive_path == temp_import_dir / 'archive'
            assert manager.failed_path == temp_import_dir / 'failed'
    
    def test_file_discovery_integration(self, temp_import_dir):
        """Test file discovery integrates with view manager."""
        from fm.modules.update_data.view_context_manager import UpdateDataViewManager
        
        # Create test CSV file
        test_file = temp_import_dir / "test.csv"
        test_file.write_text("date,amount,description\n2024-01-01,100.00,Test")
        
        manager = UpdateDataViewManager()
        status = manager.get_auto_import_status()
        
        assert str(temp_import_dir) in status['path']
        assert len(status['pending_files']) >= 1
```

**Verification**: Tests pass and follow existing test patterns

---

### Task 8: Feature Flag Integration [20 min]
**File**: [`flatmate/src/fm/core/config/feature_flags.py`](flatmate/src/fm/core/config/feature_flags.py:1)

Add feature flag for gradual rollout:

```python
class FeatureFlags:
    """Feature flags for gradual rollout."""
    
    AUTO_IMPORT_ENABLED = 'feature.auto_import_enabled'
    
    @classmethod
    def is_auto_import_enabled(cls) -> bool:
        """Check if auto-import feature is enabled."""
        return config.get_value(cls.AUTO_IMPORT_ENABLED, False)
```

**Verification**: Feature flag can be toggled via configuration

---

## Integration Checklist

### Pre-Implementation
- [ ] All existing tests pass
- [ ] Configuration system is accessible
- [ ] Dependencies are available

### During Implementation
- [ ] Each task completes within 20 minutes
- [ ] Code follows existing patterns
- [ ] No architectural changes required

### Post-Implementation
- [ ] All new tests pass
- [ ] Manual testing confirms functionality
- [ ] Feature flag controls rollout
- [ ] Documentation updated

---

## Rollback Strategy

If issues arise, disable via feature flag:
```python
# In config file
[feature]
auto_import_enabled = false
```

---

## Success Criteria

1. **Configuration**: Auto-import keys available in configuration system
2. **Service**: AutoImportManager integrates without circular dependencies
3. **UI**: Toggle switch appears in settings panel
4. **Testing**: Integration tests pass
5. **Feature Flag**: Can be enabled/disabled via configuration
6. **Pattern Consistency**: Uses existing MVP patterns exclusively

---

## Next Steps

1. Complete all 8 atomic tasks in sequence
2. Run integration tests
3. Manual testing with sample CSV files
4. Update user documentation
5. Enable feature flag for gradual rollout
