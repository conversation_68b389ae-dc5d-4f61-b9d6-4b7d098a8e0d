* Refined UI Proposal: One Canvas, Two Workflows

**Date**: 2025-07-22
**Status**: Proposed

---

## 1. Executive Summary

This document outlines a refined, incremental approach for redesigning the "Update Data" module. The goal is to create a simple, intuitive user experience that seamlessly integrates auto-import functionality while preserving the app's powerful legacy file-processing features.

The core concept is **"One Canvas, Two Workflows"**. The UI will dynamically adapt ("morph") based on a single user choice, without requiring a complete redesign or discarding existing UI components.


---

## 2. The Core Principle: A Single Control

The entire UI configuration for the "Update Data" module will be driven by a single checkbox:

`[✓] Update Database`

This control determines which of the two primary workflows is active. By default, it will be **checked**.

---

## 3. Workflow 1: "Database Mode" (Default)

This is the primary workflow, optimized for the most common user task: getting new statement data into the application's central database.

-   **Trigger:** The `[✓] Update Database` box is **checked**.
-   **Goal:** Provide a streamlined, automated, and informative import experience.

#### UI Configuration in Database Mode:

1.  **Adapt Controls:**
    -   The **`Source`** dropdown/combo-box will be set to display "Auto-Import Folder" (or the specific path) when new files are detected automatically.
    -   The **`Save Location`** control's purpose is refined to setting the **archive folder** for processed files. It remains visible.
    -   The **`Auto-Import`** configuration controls (e.g., 'Set auto-import folder...') remain visible and prominent.

2.  **Activate the Dashboard:**
    -   The main center panel will function as a **Dashboard**, providing at-a-glance information:
        -   **File Tree:** The existing file tree view will be displayed, showing any new files found by the auto-import service.
        -   **Status Info:** Displays the current auto-import status (`ACTIVE`, `DISABLED`, `No new files found`, etc.).
        -   **Database Stats:** Shows a log of recent import activity and key database metrics (e.g., number of unique accounts, transaction date range), as mentioned in other discussion documents.

3.  **Clarify the Action:**
    -   The primary action button will be clearly labeled `[Update Database]`.

---

## 4. Workflow 2: "File Utility Mode"

This workflow preserves the application's original functionality as a powerful, manual tool for merging and processing CSV files, completely independent of the database.

-   **Trigger:** The `[✓] Update Database` box is **unchecked**.
-   **Goal:** Allow for flexible, manual processing of source files into a new, user-specified output file.

#### UI Configuration in File Utility Mode:

1.  **Adapt Controls:**
    -   The **`Auto-Import`** configuration controls will be **hidden**. While the service may find files, this mode is for manual, one-off tasks, so configuring automation is not relevant.
    -   The **`Source Files`** and **`Save Location`** panels will be **visible**, becoming the primary focus.

2.  **Activate the Staging Area:**
    -   The main center panel will use the existing **File Staging Area** (the file tree view) to display files the user has manually selected for processing.

3.  **Clarify the Action:**
    -   The primary action button will be labeled `[Process Files]`.

---

## 5. Future Development & Out-of-Scope Items

-   **Options & Settings Panel:** Options not listed here will be relegated to a dedicated "Options and Settings Panel" to be implemented in a future update. This keeps the main interface clean and action-focused.
-   **Legacy Popup Details:** Any unimplemented configuration details from the original auto-import pop-up dialog will be preserved and considered for inclusion in the future Options & Settings panel.
-   **Tracking:** These items will be tracked in a separate `future_development.md` document.

---

## 6. Benefits of this Approach

-   **Preserves Design Work:** Reuses all existing UI components, panels, and widgets.
-   **Creates a "Morphic" UI:** The interface logically adapts to the user's intent, fulfilling the original design goal.
-   **Simplifies Development:** Avoids building two separate screens by using conditional visibility for components on a single screen.
-   **Intuitive User Experience:** The user makes one clear choice (`Am I updating the database or just managing files?`), and the UI configures itself to support that task.
-   **Gets Rid of the Popup:** It directly replaces the "clunky pop up window" for auto-import with a seamlessly integrated solution.


```mermaid
flowchart TD
    subgraph Database_Mode["Database Mode (Update Database)"]
        DB_Start["App Starts or Switches to Database Mode"]
        DB_Config["Configure Auto-Import and Archive Settings"]
        DB_Source["Select Source Location<br>(Any folder - not just auto-import)"]
        DB_Dashboard["Show Dashboard:<br>- File Tree (new files)<br>- Import Status<br>- DB Stats"]
        DB_Action["User Clicks Update Database"]
        DB_Process["Process Files:<br>- Import to Database<br>- Archive Originals<br>- Update Master CSV if exists"]
        DB_Result["Show Results:<br>- Success/Errors<br>- Updated Dashboard"]
        
        DB_Start --> DB_Config --> DB_Source --> DB_Dashboard --> DB_Action --> DB_Process --> DB_Result
    end

    subgraph File_Utility_Mode["File Utility Mode (No Database Update)"]
        FU_Start["App Starts or Switches to File Utility Mode"]
        FU_Source["Select Source Files<br>(Any location)"]
        FU_Dest["Select Save Location<br>(Output file destination)"]
        FU_Stage["Show File Staging Area"]
        FU_Action["User Clicks Process Files"]
        FU_Process["Process/Merge Files:<br>- Create new output file<br>- Optional: Update Master CSV"]
        FU_Result["Show Results:<br>- Success/Errors<br>- Updated file list"]
        
        FU_Start --> FU_Source --> FU_Dest --> FU_Stage --> FU_Action --> FU_Process --> FU_Result
    end

    subgraph Flexibility_Features["Key Flexibility Features"]
        Any_Source["Can read files from ANY location"]
        Master_CSV["Optional Master CSV maintenance"]
        Auto_Archive["Automatic archive management"]
    end
```