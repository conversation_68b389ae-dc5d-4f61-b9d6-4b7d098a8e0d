# Enhanced Widget Architecture Design

**Date**: 2025-07-22  
**Session**: REFACTOR_gui_components_hierarchical  
**Design Type**: Detailed Architecture Specification

---

## Architecture Overview

This design implements the App-Wide Widget Pattern established in the codebase while introducing hierarchical organization and enhanced capabilities. The architecture maintains backward compatibility while providing a foundation for scalable widget development.

## Core Design Principles

### 1. App-Wide Widget Pattern Compliance
- **Configuration**: Instance runtime defaults via `configure()` method
- **Content**: Data/widgets to display via `set_content()` method  
- **Visibility**: Control when/how widget is shown
- **Dynamic Methods**: Runtime changes from user interaction
- **Chainable Methods**: All methods return `self` for fluent interface

### 2. Hierarchical Organization
- **Category-based Structure**: Widgets organized by functional type
- **Clear Separation**: Code, styles, and configuration separated
- **Modular Design**: Each widget type in its own module

### 3. Backward Compatibility
- **Existing APIs**: All current public APIs maintained exactly
- **Import Paths**: Both old and new import paths supported
- **Signal Compatibility**: All existing signals preserved

## Base Widget Architecture

### BaseWidget Implementation
```python
# base/base_widget.py
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional, Dict
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal

from ..config.widget_config import BaseWidgetConfig
from ..styles.loader import StyleLoader

class BaseWidget(QWidget, ABC):
    """Base widget following App-Wide Widget Pattern.
    
    Provides consistent API for all shared widgets with:
    - Configuration management
    - Content handling  
    - Style loading
    - Runtime flexibility
    """
    
    # Standard signals that subclasses can override
    configuration_changed = Signal(dict)
    content_changed = Signal()
    
    def __init__(self, parent=None):
        """Initialize with sensible defaults."""
        super().__init__(parent)
        self._config = self._get_default_config()
        self._content = None
        self._is_shown = False
        self._setup_ui()
        self._apply_default_styles()
    
    # === ABSTRACT METHODS (Must be implemented by subclasses) ===
    
    @abstractmethod
    def _get_default_config(self) -> BaseWidgetConfig:
        """Return default configuration for this widget type."""
        pass
    
    @abstractmethod
    def _setup_ui(self):
        """Initialize UI components."""
        pass
    
    @abstractmethod
    def _apply_configuration(self):
        """Apply current configuration to UI."""
        pass
    
    @abstractmethod
    def _apply_content(self):
        """Apply current content to UI."""
        pass
    
    # === CONFIGURATION (Instance Runtime Defaults) ===
    
    def configure(self, **kwargs) -> 'BaseWidget':
        """Configure widget behavior and appearance.
        
        Args:
            **kwargs: Configuration options specific to widget type
            
        Returns:
            self for method chaining
        """
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}")
        
        # Apply configuration if already shown
        if self._is_shown:
            self._apply_configuration()
            
        self.configuration_changed.emit(kwargs)
        return self
    
    # === CONTENT ===
    
    def set_content(self, content: Any) -> 'BaseWidget':
        """Set widget content.
        
        Args:
            content: Content to display (type varies by widget)
            
        Returns:
            self for method chaining
        """
        self._content = content
        
        if self._is_shown:
            self._apply_content()
            
        self.content_changed.emit()
        return self
    
    # === VISIBILITY ===
    
    def show(self):
        """Show widget and apply all configurations."""
        if not self._is_shown:
            self._apply_configuration()
            self._apply_content()
            self._is_shown = True
        super().show()
    
    def hide(self):
        """Hide widget."""
        self._is_shown = False
        super().hide()
    
    # === STYLE MANAGEMENT ===
    
    def _apply_default_styles(self):
        """Apply default styles for this widget type."""
        widget_type = self.__class__.__name__.lower()
        StyleLoader.apply_widget_styles(self, widget_type)
    
    def refresh_styles(self):
        """Refresh widget styles (useful for theme changes)."""
        self._apply_default_styles()
    
    # === UTILITY METHODS ===
    
    def get_config(self) -> BaseWidgetConfig:
        """Get current configuration (read-only)."""
        return self._config
    
    def get_content(self) -> Any:
        """Get current content."""
        return self._content
    
    def reset_to_defaults(self) -> 'BaseWidget':
        """Reset widget to default configuration."""
        self._config = self._get_default_config()
        if self._is_shown:
            self._apply_configuration()
        return self
```

## Configuration System Architecture

### Base Configuration Classes
```python
# config/widget_config.py
from dataclasses import dataclass, field
from typing import Optional, Any, Dict

@dataclass
class BaseWidgetConfig:
    """Base configuration for all widgets."""
    # Styling
    style_type: str = "default"
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    # Behavior
    enabled: bool = True
    tooltip: Optional[str] = None
    
    # Layout
    minimum_width: Optional[int] = None
    minimum_height: Optional[int] = None
    maximum_width: Optional[int] = None
    maximum_height: Optional[int] = None

@dataclass  
class ButtonConfig(BaseWidgetConfig):
    """Configuration for button widgets."""
    style_type: str = "action_btn"
    text: str = ""
    icon: Optional[str] = None
    
@dataclass
class CheckBoxConfig(BaseWidgetConfig):
    """Configuration for checkbox widgets."""
    checked: bool = False
    label_text: str = ""
    label_position: str = "right"  # "left" or "right"
    
@dataclass
class LabelConfig(BaseWidgetConfig):
    """Configuration for label widgets."""
    text: str = ""
    alignment: str = "left"  # "left", "center", "right"
    word_wrap: bool = False
    
@dataclass
class OptionMenuConfig(BaseWidgetConfig):
    """Configuration for option menu widgets."""
    options: list = field(default_factory=list)
    selected_option: Optional[str] = None
    placeholder_text: str = "Select option..."
    editable: bool = False
```

### Configuration Factory
```python
# config/factory.py
from typing import Type, TypeVar
from .widget_config import (
    BaseWidgetConfig, ButtonConfig, CheckBoxConfig, 
    LabelConfig, OptionMenuConfig
)

T = TypeVar('T', bound=BaseWidgetConfig)

class ConfigFactory:
    """Factory for creating widget configurations."""
    
    _config_map = {
        'button': ButtonConfig,
        'checkbox': CheckBoxConfig,
        'label': LabelConfig,
        'option_menu': OptionMenuConfig,
    }
    
    @classmethod
    def create_config(cls, widget_type: str, **kwargs) -> BaseWidgetConfig:
        """Create configuration for widget type."""
        config_class = cls._config_map.get(widget_type, BaseWidgetConfig)
        return config_class(**kwargs)
    
    @classmethod
    def register_config(cls, widget_type: str, config_class: Type[T]):
        """Register new configuration type."""
        cls._config_map[widget_type] = config_class
```

## Style Loading Architecture

### Style Loader Implementation
```python
# styles/loader.py
from pathlib import Path
from typing import Optional, Dict
from PySide6.QtWidgets import QWidget, QApplication

class StyleLoader:
    """Centralized style loading for widgets."""
    
    _styles_cache: Dict[str, str] = {}
    _styles_dir = Path(__file__).parent
    
    @classmethod
    def load_base_styles(cls) -> str:
        """Load base styles for all widgets."""
        return cls._load_style_file("base.qss")
    
    @classmethod
    def load_widget_styles(cls, widget_type: str) -> str:
        """Load styles for specific widget type."""
        style_file = f"widgets/{widget_type}.qss"
        return cls._load_style_file(style_file)
    
    @classmethod
    def apply_widget_styles(cls, widget: QWidget, widget_type: str):
        """Apply styles to widget instance."""
        base_styles = cls.load_base_styles()
        widget_styles = cls.load_widget_styles(widget_type)
        
        combined_styles = f"{base_styles}\n{widget_styles}"
        widget.setStyleSheet(combined_styles)
    
    @classmethod
    def _load_style_file(cls, filename: str) -> str:
        """Load and cache style file."""
        if filename in cls._styles_cache:
            return cls._styles_cache[filename]
        
        style_path = cls._styles_dir / filename
        if style_path.exists():
            with open(style_path, 'r', encoding='utf-8') as f:
                content = f.read()
                cls._styles_cache[filename] = content
                return content
        
        return ""
    
    @classmethod
    def clear_cache(cls):
        """Clear style cache (useful for development)."""
        cls._styles_cache.clear()
    
    @classmethod
    def reload_styles(cls):
        """Reload all styles from disk."""
        cls.clear_cache()
        # Trigger style refresh on all widgets
        app = QApplication.instance()
        if app:
            app.setStyleSheet(app.styleSheet())
```

## Widget Implementation Examples

### Enhanced Button Implementation
```python
# buttons/action_button.py
from PySide6.QtWidgets import QPushButton
from PySide6.QtCore import Signal

from ..base.base_widget import BaseWidget
from ..config.widget_config import ButtonConfig

class ActionButton(BaseWidget):
    """Enhanced action button following App-Wide Widget Pattern."""
    
    # Button-specific signals
    clicked = Signal()
    
    def __init__(self, text: str = "", parent=None):
        self._text = text
        super().__init__(parent)
    
    def _get_default_config(self) -> ButtonConfig:
        """Return default configuration for action buttons."""
        return ButtonConfig(
            style_type="action_btn",
            text=self._text
        )
    
    def _setup_ui(self):
        """Initialize button UI."""
        # Create internal QPushButton
        self._button = QPushButton(self)
        self._button.clicked.connect(self.clicked.emit)
        
        # Set up layout
        from PySide6.QtWidgets import QVBoxLayout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self._button)
    
    def _apply_configuration(self):
        """Apply configuration to button."""
        self._button.setText(self._config.text)
        self._button.setEnabled(self._config.enabled)
        self._button.setProperty("type", self._config.style_type)
        
        if self._config.tooltip:
            self._button.setToolTip(self._config.tooltip)
    
    def _apply_content(self):
        """Apply content to button."""
        if self._content:
            self._button.setText(str(self._content))
    
    # === BUTTON-SPECIFIC METHODS ===
    
    def set_text(self, text: str) -> 'ActionButton':
        """Set button text."""
        return self.configure(text=text)
    
    def click(self):
        """Programmatically click the button."""
        self._button.click()
```

---

**Architecture Design Complete**: Foundation established for hierarchical widget migration.
**Next Step**: Create migration timeline and validation checklist.
