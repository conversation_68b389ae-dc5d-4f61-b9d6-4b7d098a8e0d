@_discussion.md okay, so lets have think a think what should we do ....
I think we have good basic "quick search" functionality in the search bar the AND search (imlicit) and exlude logic `-term` but we dont have any or logic because - I dont think its compatible with live filtering 

what different styles of search are their? what makes the most sense for a quick search tool

Because I think the more advanced logic may need to be implemented in a dedicated tool 

I'm noticing the system is still laggy even though I have closed most programs 
other than windsurf 

---

## Discussion Points
- Current quick search supports AND and exclude (`-term`), but not OR logic.
- Live filtering is problematic with advanced/complex queries.
- Advanced boolean logic may not be suitable for live search.
- System lag and UI freezes observed during complex filtering.
- User feedback: quick search should remain simple and responsive.

## Options
- **Option 1:** Keep quick search simple (AND, exclude only), require explicit action for advanced queries.
- **Option 2:** Add support for OR/complex logic, but disable live filtering for such queries (apply on Enter/blur).
  - *Enter/blur: Only apply the filter when the user presses Enter or the input field loses focus (user clicks away or tabs out), not on every keystroke.*
- **Option 3:** Implement a dedicated advanced filter tool for power users, separate from quick search.
- **Option 4:** Profile and optimise filtering logic to address lag and freezes, regardless of search style.

## Chosen Solution ✅ IMPLEMENTED
Live filtering will immediately stop as soon as the user enters any operator or a character used in an operator (e.g., `O`, `R`, `/`, `(`, `)`, etc.). At that point:
- Live filtering is disabled for the current query.
- An "Apply" button (or similar visual cue) appears, prompting the user to confirm and run the advanced filter.
- This ensures live filtering remains fast and simple for basic queries, while advanced logic is handled explicitly and avoids UI lag or errors.

## Implementation Details (2025-07-19)

### Smart Dispatching Solution
- **Dispatcher**: SearchQueryParser's `_is_simple_query()` method serves as the intelligent dispatcher
- **Performance**: Fast character-based detection (|, /, (, ), ") before expensive regex checks
- **Debouncing**: 150ms debounce for complex queries prevents UI freezes during typing
- **Fast Path**: Obviously simple queries get immediate live filtering

### Visual Feedback Implemented
- **Dynamic Apply Button**: Shows/hides automatically based on query complexity
- **Input Styling**: Green border indicates advanced mode
- **Placeholder Updates**: "Advanced search mode - press Enter or click Apply to filter"
- **Consistent Theming**: Apply button uses primary green color scheme

### System Column Filtering
- **Problem Solved**: DB UID, Source UID, Is Deleted no longer visible to users
- **Implementation**: ColumnSelector filters out 'db_system' group columns
- **Preserved**: User-meaningful columns like "Unique ID" remain visible

### Performance Optimizations
- **Error Handling**: Comprehensive try-catch prevents crashes
- **Pattern Limits**: 500 character limit prevents excessive parsing
- **Graceful Fallback**: Returns True on parsing errors to avoid hiding all data
- **Memory Efficient**: Reuses singleton search parser instance

## Status: COMPLETE ✅
All acceptance criteria met, performance issues resolved, user experience optimized.